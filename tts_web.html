<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音合成工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .slider-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .slider {
            flex: 1;
        }
        .slider-value {
            min-width: 50px;
            text-align: center;
            font-weight: bold;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .audio-container {
            margin-top: 20px;
            text-align: center;
        }
        .config-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            color: #dc3545;
            margin-top: 10px;
        }
        .success {
            color: #28a745;
            margin-top: 10px;
        }
        .config-note {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音合成工具</h1>
        
        <div class="config-section">
            <h3>API配置</h3>
            <div class="form-group">
                <label for="appId">App ID:</label>
                <input type="text" id="appId" placeholder="请输入您的App ID">
                <div class="config-note">如果config.js中已配置，此处可留空</div>
            </div>
            <div class="form-group">
                <label for="accessToken">Access Token:</label>
                <input type="text" id="accessToken" placeholder="请输入您的Access Token">
                <div class="config-note">如果config.js中已配置，此处可留空</div>
            </div>
        </div>

        <div class="form-group">
            <label for="text">合成文本:</label>
            <textarea id="text" placeholder="请输入要合成的文本...">你好，欢迎使用语音合成服务！</textarea>
        </div>

        <div class="form-group">
            <label for="language">语种:</label>
            <select id="language"></select>
        </div>

        <div class="form-group">
            <label for="voice">音色:</label>
            <select id="voice"></select>
        </div>

        <div class="form-group">
            <label for="emotion">情感:</label>
            <select id="emotion"></select>
        </div>

        <div class="form-group">
            <label for="speed">语速:</label>
            <div class="slider-container">
                <input type="range" id="speed" class="slider" min="0.5" max="2.0" step="0.1" value="1.0">
                <span class="slider-value" id="speedValue">1.0</span>
            </div>
        </div>

        <div class="form-group">
            <label for="volume">音量:</label>
            <div class="slider-container">
                <input type="range" id="volume" class="slider" min="0.1" max="2.0" step="0.1" value="1.0">
                <span class="slider-value" id="volumeValue">1.0</span>
            </div>
        </div>

        <div class="form-group">
            <label for="pitch">音调:</label>
            <div class="slider-container">
                <input type="range" id="pitch" class="slider" min="0.5" max="2.0" step="0.1" value="1.0">
                <span class="slider-value" id="pitchValue">1.0</span>
            </div>
        </div>

        <div style="text-align: center;">
            <button onclick="synthesize()" id="synthesizeBtn">🎵 开始合成</button>
            <button onclick="clearAll()">🗑️ 清空</button>
        </div>

        <div id="message"></div>
        <div class="audio-container" id="audioContainer"></div>
    </div>

    <script src="config.js"></script>
    <script>
        // 初始化页面
        function initializePage() {
            // 初始化语种选项
            const languageSelect = document.getElementById('language');
            const languages = [
                {value: 'zh', text: '中文'},
                {value: 'en', text: '英文'},
                {value: 'ja', text: '日文'},
                {value: 'ko', text: '韩文'}
            ];
            
            languages.forEach(lang => {
                const option = document.createElement('option');
                option.value = lang.value;
                option.textContent = lang.text;
                languageSelect.appendChild(option);
            });
            
            // 初始化情感选项
            const emotionSelect = document.getElementById('emotion');
            TTS_CONFIG.emotions.forEach(emotion => {
                const option = document.createElement('option');
                option.value = emotion.value;
                option.textContent = emotion.text;
                emotionSelect.appendChild(option);
            });
            
            // 设置默认值
            languageSelect.value = TTS_CONFIG.defaults.language;
            emotionSelect.value = TTS_CONFIG.defaults.emotion;
            document.getElementById('speed').value = TTS_CONFIG.defaults.speed;
            document.getElementById('volume').value = TTS_CONFIG.defaults.volume;
            document.getElementById('pitch').value = TTS_CONFIG.defaults.pitch;
            
            // 从配置文件加载API信息
            if (TTS_CONFIG.api.appId) {
                document.getElementById('appId').value = TTS_CONFIG.api.appId;
            }
            if (TTS_CONFIG.api.accessToken) {
                document.getElementById('accessToken').value = TTS_CONFIG.api.accessToken;
            }
            
            // 初始化音色选项
            updateVoiceOptions();
            updateSliderValues();
        }

        // 更新音色选项
        function updateVoiceOptions() {
            const language = document.getElementById('language').value;
            const voiceSelect = document.getElementById('voice');
            voiceSelect.innerHTML = '';
            
            TTS_CONFIG.voices[language].forEach(voice => {
                const option = document.createElement('option');
                option.value = voice.value;
                option.textContent = voice.text;
                voiceSelect.appendChild(option);
            });
        }

        // 更新滑块值显示
        function updateSliderValues() {
            document.getElementById('speedValue').textContent = document.getElementById('speed').value;
            document.getElementById('volumeValue').textContent = document.getElementById('volume').value;
            document.getElementById('pitchValue').textContent = document.getElementById('pitch').value;
        }

        // 事件监听器
        document.getElementById('language').addEventListener('change', updateVoiceOptions);
        
        document.getElementById('speed').addEventListener('input', function() {
            document.getElementById('speedValue').textContent = this.value;
        });
        
        document.getElementById('volume').addEventListener('input', function() {
            document.getElementById('volumeValue').textContent = this.value;
        });
        
        document.getElementById('pitch').addEventListener('input', function() {
            document.getElementById('pitchValue').textContent = this.value;
        });

        async function synthesize() {
            // 获取配置信息，优先使用输入框的值
            const appId = document.getElementById('appId').value || TTS_CONFIG.api.appId;
            const accessToken = document.getElementById('accessToken').value || TTS_CONFIG.api.accessToken;
            const text = document.getElementById('text').value;
            const voice = document.getElementById('voice').value;
            const emotion = document.getElementById('emotion').value;
            const speed = document.getElementById('speed').value;
            const volume = document.getElementById('volume').value;
            const pitch = document.getElementById('pitch').value;
            
            if (!appId || !accessToken || !text) {
                showMessage('请填写完整的配置信息和文本', 'error');
                return;
            }
            
            const btn = document.getElementById('synthesizeBtn');
            btn.disabled = true;
            btn.textContent = '合成中...';
            
            try {
                const response = await fetch(TTS_CONFIG.api.endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${accessToken}`
                    },
                    body: JSON.stringify({
                        app: {
                            appid: appId,
                            token: accessToken
                        },
                        user: {
                            uid: "user_" + Date.now()
                        },
                        audio: {
                            voice_type: voice,
                            language: document.getElementById('language').value,
                            speed_ratio: parseFloat(speed),
                            volume_ratio: parseFloat(volume),
                            pitch_ratio: parseFloat(pitch),
                            emotion: emotion,
                            encoding: TTS_CONFIG.defaults.encoding
                        },
                        request: {
                            reqid: "req_" + Date.now(),
                            text: text,
                            text_type: "plain",
                            operation: "query"
                        }
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.code === 3000) {
                        const audioData = data.data;
                        const audioBlob = new Blob([Uint8Array.from(atob(audioData), c => c.charCodeAt(0))], {type: 'audio/mp3'});
                        const audioUrl = URL.createObjectURL(audioBlob);
                        
                        const audioContainer = document.getElementById('audioContainer');
                        audioContainer.innerHTML = `
                            <audio controls style="width: 100%; margin-top: 10px;">
                                <source src="${audioUrl}" type="audio/mp3">
                                您的浏览器不支持音频播放。
                            </audio>
                            <br>
                            <a href="${audioUrl}" download="tts_audio.mp3" style="margin-top: 10px; display: inline-block;">
                                📥 下载音频文件
                            </a>
                        `;
                        
                        showMessage('语音合成成功！', 'success');
                    } else {
                        showMessage(`合成失败: ${data.message}`, 'error');
                    }
                } else {
                    showMessage(`请求失败: ${response.status}`, 'error');
                }
            } catch (error) {
                showMessage(`网络错误: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = '🎵 开始合成';
            }
        }
        
        function clearAll() {
            document.getElementById('text').value = '';
            document.getElementById('audioContainer').innerHTML = '';
            document.getElementById('message').innerHTML = '';
        }
        
        function showMessage(text, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.className = type;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);
    </script>
</body>
</html>
