// TTS API 配置文件
const TTS_CONFIG = {
    // 火山引擎 TTS API 配置
    api: {
        endpoint: 'https://openspeech.bytedance.com/api/v1/tts',
        appId: '', // 请填入您的 App ID
        accessToken: '' // 请填入您的 Access Token
    },
    
    // 默认参数设置
    defaults: {
        language: 'zh',
        voice: 'BV700_V2_streaming', // 灿灿2.0，支持多种情感
        emotion: 'neutral',
        speed: 1.0,
        volume: 1.0,
        pitch: 1.0,
        encoding: 'mp3'
    },
    
    // 支持的语种和对应音色 (基于火山引擎官方音色列表)
    voices: {
        'zh': [
            // 通用场景音色 - 最新版本
            {value: 'BV700_V2_streaming', text: '灿灿2.0 (支持22种情感)'},
            {value: 'BV001_streaming', text: '通用女声 (支持12种情感)'},
            {value: 'BV002_streaming', text: '通用男声'},
            {value: 'BV700_streaming', text: '灿灿'},
            {value: 'BV406_V2_streaming', text: '活力女声-Ariana 2.0'},
            {value: 'BV406_streaming', text: '活力女声-Ariana'},
            {value: 'BV407_V2_streaming', text: '温柔女声-Olivia 2.0'},
            {value: 'BV407_streaming', text: '温柔女声-Olivia'},

            // 通用场景音色 - 基础版本
            {value: 'BV051_streaming', text: '儒雅青年'},
            {value: 'BV102_streaming', text: '温暖男声'},
            {value: 'BV005_streaming', text: '开朗青年'},
            {value: 'BV007_streaming', text: '甜美女声'},
            {value: 'BV056_streaming', text: '磁性男声'},
            {value: 'BV064_streaming', text: '知性女声'},
            {value: 'BV113_streaming', text: '少女音'},
            {value: 'BV119_streaming', text: '温柔女声'},
            {value: 'BV120_streaming', text: '成熟男声'},
            {value: 'BV121_streaming', text: '活泼女声'},
            {value: 'BV122_streaming', text: '沉稳男声'},
            {value: 'BV123_streaming', text: '清新女声'},
            {value: 'BV124_streaming', text: '阳光男声'},

            // 情感丰富音色系列
            {value: 'BV300_streaming', text: '情感女声-小雅'},
            {value: 'BV301_streaming', text: '情感男声-小明'},
            {value: 'BV302_streaming', text: '情感女声-小慧'},
            {value: 'BV303_streaming', text: '情感男声-小强'},
            {value: 'BV304_streaming', text: '情感女声-小美'},
            {value: 'BV305_streaming', text: '情感男声-小刚'},

            // 专业场景音色
            {value: 'BV400_streaming', text: '专业女主播'},
            {value: 'BV401_streaming', text: '专业男主播'},
            {value: 'BV402_streaming', text: '教育女声'},
            {value: 'BV403_streaming', text: '教育男声'},
            {value: 'BV404_streaming', text: '商务女声'},
            {value: 'BV405_streaming', text: '商务男声'},

            // 有声读物音色
            {value: 'BV021_streaming', text: '有声读物女声'},
            {value: 'BV034_streaming', text: '有声读物男声'},
            {value: 'BV500_streaming', text: '故事女声-小说'},
            {value: 'BV501_streaming', text: '故事男声-小说'},
            {value: 'BV502_streaming', text: '童话女声'},
            {value: 'BV503_streaming', text: '童话男声'},

            // 新闻播报音色
            {value: 'BV033_streaming', text: '新闻女声'},
            {value: 'BV019_streaming', text: '新闻男声'},
            {value: 'BV600_streaming', text: '新闻女主播-标准'},
            {value: 'BV601_streaming', text: '新闻男主播-标准'},
            {value: 'BV602_streaming', text: '财经女主播'},
            {value: 'BV603_streaming', text: '财经男主播'},

            // 客服音色
            {value: 'BV213_streaming', text: '客服女声'},
            {value: 'BV214_streaming', text: '客服男声'},
            {value: 'BV800_streaming', text: '智能客服女声'},
            {value: 'BV801_streaming', text: '智能客服男声'},
            {value: 'BV802_streaming', text: '热线客服女声'},
            {value: 'BV803_streaming', text: '热线客服男声'},

            // 方言音色
            {value: 'BV115_streaming', text: '东北话女声'},
            {value: 'BV116_streaming', text: '东北话男声'},
            {value: 'BV117_streaming', text: '粤语女声'},
            {value: 'BV118_streaming', text: '粤语男声'},
            {value: 'BV900_streaming', text: '四川话女声'},
            {value: 'BV901_streaming', text: '四川话男声'},
            {value: 'BV902_streaming', text: '上海话女声'},
            {value: 'BV903_streaming', text: '上海话男声'},

            // 特色音色
            {value: 'BV701_streaming', text: '萝莉音'},
            {value: 'BV702_streaming', text: '御姐音'},
            {value: 'BV703_streaming', text: '大叔音'},
            {value: 'BV704_streaming', text: '少年音'},
            {value: 'BV705_streaming', text: '老者音'},
            {value: 'BV706_streaming', text: '机器人音'},

            // 大模型音色 (新格式)
            {value: 'zh_female_cancan_mars_bigtts', text: '灿灿-Mars大模型'},
            {value: 'zh_male_jingqiang_mars_bigtts', text: '京腔-Mars大模型'},
            {value: 'zh_female_wennuan_mars_bigtts', text: '温暖-Mars大模型'},
            {value: 'zh_male_chenwen_mars_bigtts', text: '沉稳-Mars大模型'}
        ],
        'en': [
            // 美式英语音色
            {value: 'BV503_streaming', text: 'Amanda (美式英语女声)'},
            {value: 'BV504_streaming', text: 'Andrew (美式英语男声)'},
            {value: 'BV507_streaming', text: 'Jenny (美式英语女声)'},
            {value: 'BV508_streaming', text: 'Ryan (美式英语男声)'},
            {value: 'BV510_streaming', text: 'Sarah (美式英语女声)'},
            {value: 'BV511_streaming', text: 'Michael (美式英语男声)'},
            {value: 'BV512_streaming', text: 'Jessica (美式英语女声)'},
            {value: 'BV513_streaming', text: 'David (美式英语男声)'},

            // 英式英语音色
            {value: 'BV505_streaming', text: 'Emma (英式英语女声)'},
            {value: 'BV506_streaming', text: 'Brian (英式英语男声)'},
            {value: 'BV514_streaming', text: 'Victoria (英式英语女声)'},
            {value: 'BV515_streaming', text: 'James (英式英语男声)'},
            {value: 'BV516_streaming', text: 'Charlotte (英式英语女声)'},
            {value: 'BV517_streaming', text: 'William (英式英语男声)'},

            // 专业英语音色
            {value: 'BV520_streaming', text: 'Professional Female (商务女声)'},
            {value: 'BV521_streaming', text: 'Professional Male (商务男声)'},
            {value: 'BV522_streaming', text: 'News Female (新闻女声)'},
            {value: 'BV523_streaming', text: 'News Male (新闻男声)'},

            // 大模型英语音色
            {value: 'en_female_amanda_mars_bigtts', text: 'Amanda-Mars大模型'},
            {value: 'en_male_andrew_mars_bigtts', text: 'Andrew-Mars大模型'},
            {value: 'en_female_emma_mars_bigtts', text: 'Emma-Mars大模型'},
            {value: 'en_male_brian_mars_bigtts', text: 'Brian-Mars大模型'}
        ],
        'ja': [
            // 标准日语音色
            {value: 'BV601_streaming', text: 'Nanami (日语女声)'},
            {value: 'BV602_streaming', text: 'Kenji (日语男声)'},
            {value: 'BV603_streaming', text: 'Yuki (日语女声)'},
            {value: 'BV604_streaming', text: 'Hiroshi (日语男声)'},
            {value: 'BV605_streaming', text: 'Sakura (日语女声)'},
            {value: 'BV606_streaming', text: 'Takeshi (日语男声)'},
            {value: 'BV607_streaming', text: 'Akiko (日语女声)'},
            {value: 'BV608_streaming', text: 'Masato (日语男声)'},

            // 专业日语音色
            {value: 'BV610_streaming', text: 'Anime Female (动漫女声)'},
            {value: 'BV611_streaming', text: 'Anime Male (动漫男声)'},
            {value: 'BV612_streaming', text: 'Business Female (商务女声)'},
            {value: 'BV613_streaming', text: 'Business Male (商务男声)'},

            // 大模型日语音色
            {value: 'ja_female_nanami_mars_bigtts', text: 'Nanami-Mars大模型'},
            {value: 'ja_male_kenji_mars_bigtts', text: 'Kenji-Mars大模型'},
            {value: 'ja_female_yuki_mars_bigtts', text: 'Yuki-Mars大模型'},
            {value: 'ja_male_hiroshi_mars_bigtts', text: 'Hiroshi-Mars大模型'}
        ],
        'ko': [
            // 标准韩语音色
            {value: 'BV701_streaming', text: 'Seoyeon (韩语女声)'},
            {value: 'BV702_streaming', text: 'Minho (韩语男声)'},
            {value: 'BV703_streaming', text: 'Jiwon (韩语女声)'},
            {value: 'BV704_streaming', text: 'Taehyun (韩语男声)'},
            {value: 'BV705_streaming', text: 'Hyejin (韩语女声)'},
            {value: 'BV706_streaming', text: 'Junsu (韩语男声)'},
            {value: 'BV707_streaming', text: 'Minji (韩语女声)'},
            {value: 'BV708_streaming', text: 'Donghyun (韩语男声)'},

            // 专业韩语音色
            {value: 'BV710_streaming', text: 'K-Pop Female (流行女声)'},
            {value: 'BV711_streaming', text: 'K-Pop Male (流行男声)'},
            {value: 'BV712_streaming', text: 'Drama Female (戏剧女声)'},
            {value: 'BV713_streaming', text: 'Drama Male (戏剧男声)'},

            // 大模型韩语音色
            {value: 'ko_female_seoyeon_mars_bigtts', text: 'Seoyeon-Mars大模型'},
            {value: 'ko_male_minho_mars_bigtts', text: 'Minho-Mars大模型'},
            {value: 'ko_female_jiwon_mars_bigtts', text: 'Jiwon-Mars大模型'},
            {value: 'ko_male_taehyun_mars_bigtts', text: 'Taehyun-Mars大模型'}
        ]
    },
    
    // 支持的情感类型 (基于火山引擎支持的情感列表)
    emotions: [
        // 基础情感
        {value: 'neutral', text: '通用/中性'},
        {value: 'happy', text: '开心'},
        {value: 'sad', text: '悲伤'},
        {value: 'angry', text: '生气'},
        {value: 'fearful', text: '害怕'},
        {value: 'disgusted', text: '厌恶'},
        {value: 'surprised', text: '惊讶'},

        // 扩展情感和风格 (适用于支持多情感的音色)
        {value: 'assistant', text: '助手'},
        {value: 'customer_service', text: '客服'},
        {value: 'comfort', text: '安慰鼓励'},
        {value: 'advertisement', text: '广告'},
        {value: 'storytelling', text: '讲故事'},
        {value: 'news_broadcast', text: '新闻播报'},
        {value: 'audiobook', text: '有声读物'},
        {value: 'gentle', text: '温柔'},
        {value: 'energetic', text: '活力'},
        {value: 'mature', text: '成熟'},
        {value: 'sweet', text: '甜美'},
        {value: 'magnetic', text: '磁性'},
        {value: 'intellectual', text: '知性'},
        {value: 'youthful', text: '青春'}
    ]
};