// TTS API 配置文件
const TTS_CONFIG = {
    // 火山引擎 TTS API 配置
    api: {
        endpoint: 'https://openspeech.bytedance.com/api/v1/tts',
        appId: '', // 请填入您的 App ID
        accessToken: '' // 请填入您的 Access Token
    },
    
    // 默认参数设置
    defaults: {
        language: 'zh',
        voice: 'BV700_V2_streaming', // 灿灿2.0，支持多种情感
        emotion: 'neutral',
        speed: 1.0,
        volume: 1.0,
        pitch: 1.0,
        encoding: 'mp3'
    },
    
    // 支持的语种和对应音色 (基于火山引擎官方音色列表)
    voices: {
        'zh': [
            // 通用场景音色
            {value: 'BV700_V2_streaming', text: '灿灿2.0 (支持22种情感)'},
            {value: 'BV001_streaming', text: '通用女声 (支持12种情感)'},
            {value: 'BV002_streaming', text: '通用男声'},
            {value: 'BV700_streaming', text: '灿灿'},
            {value: 'BV406_V2_streaming', text: '活力女声-Ariana 2.0'},
            {value: 'BV406_streaming', text: '活力女声-Ariana'},
            {value: 'BV407_V2_streaming', text: '温柔女声-Olivia 2.0'},
            {value: 'BV407_streaming', text: '温柔女声-Olivia'},
            {value: 'BV051_streaming', text: '儒雅青年'},
            {value: 'BV102_streaming', text: '温暖男声'},
            {value: 'BV005_streaming', text: '开朗青年'},
            {value: 'BV007_streaming', text: '甜美女声'},
            {value: 'BV056_streaming', text: '磁性男声'},
            {value: 'BV064_streaming', text: '知性女声'},
            {value: 'BV113_streaming', text: '少女音'},
            {value: 'BV119_streaming', text: '温柔女声'},
            {value: 'BV120_streaming', text: '成熟男声'},

            // 有声读物音色
            {value: 'BV021_streaming', text: '有声读物女声'},
            {value: 'BV034_streaming', text: '有声读物男声'},

            // 新闻播报音色
            {value: 'BV033_streaming', text: '新闻女声'},
            {value: 'BV019_streaming', text: '新闻男声'},

            // 客服音色
            {value: 'BV213_streaming', text: '客服女声'},
            {value: 'BV214_streaming', text: '客服男声'},

            // 方言音色
            {value: 'BV115_streaming', text: '东北话女声'},
            {value: 'BV116_streaming', text: '东北话男声'},
            {value: 'BV117_streaming', text: '粤语女声'},
            {value: 'BV118_streaming', text: '粤语男声'}
        ],
        'en': [
            {value: 'BV503_streaming', text: 'Amanda (美式英语女声)'},
            {value: 'BV504_streaming', text: 'Andrew (美式英语男声)'},
            {value: 'BV505_streaming', text: 'Emma (英式英语女声)'},
            {value: 'BV506_streaming', text: 'Brian (英式英语男声)'},
            {value: 'BV507_streaming', text: 'Jenny (美式英语女声)'},
            {value: 'BV508_streaming', text: 'Ryan (美式英语男声)'}
        ],
        'ja': [
            {value: 'BV601_streaming', text: 'Nanami (日语女声)'},
            {value: 'BV602_streaming', text: 'Kenji (日语男声)'},
            {value: 'BV603_streaming', text: 'Yuki (日语女声)'},
            {value: 'BV604_streaming', text: 'Hiroshi (日语男声)'}
        ],
        'ko': [
            {value: 'BV701_streaming', text: 'Seoyeon (韩语女声)'},
            {value: 'BV702_streaming', text: 'Minho (韩语男声)'},
            {value: 'BV703_streaming', text: 'Jiwon (韩语女声)'},
            {value: 'BV704_streaming', text: 'Taehyun (韩语男声)'}
        ]
    },
    
    // 支持的情感类型 (基于火山引擎支持的情感列表)
    emotions: [
        // 基础情感
        {value: 'neutral', text: '通用/中性'},
        {value: 'happy', text: '开心'},
        {value: 'sad', text: '悲伤'},
        {value: 'angry', text: '生气'},
        {value: 'fearful', text: '害怕'},
        {value: 'disgusted', text: '厌恶'},
        {value: 'surprised', text: '惊讶'},

        // 扩展情感和风格 (适用于支持多情感的音色)
        {value: 'assistant', text: '助手'},
        {value: 'customer_service', text: '客服'},
        {value: 'comfort', text: '安慰鼓励'},
        {value: 'advertisement', text: '广告'},
        {value: 'storytelling', text: '讲故事'},
        {value: 'news_broadcast', text: '新闻播报'},
        {value: 'audiobook', text: '有声读物'},
        {value: 'gentle', text: '温柔'},
        {value: 'energetic', text: '活力'},
        {value: 'mature', text: '成熟'},
        {value: 'sweet', text: '甜美'},
        {value: 'magnetic', text: '磁性'},
        {value: 'intellectual', text: '知性'},
        {value: 'youthful', text: '青春'}
    ]
};