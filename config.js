// TTS API 配置文件
const TTS_CONFIG = {
    // 火山引擎 TTS API 配置
    api: {
        endpoint: 'https://openspeech.bytedance.com/api/v1/tts',
        appId: '', // 请填入您的 App ID
        accessToken: '' // 请填入您的 Access Token
    },
    
    // 默认参数设置
    defaults: {
        language: 'zh',
        voice: 'zh_female_shuangkuaisisi_moon_bigtts',
        emotion: 'neutral',
        speed: 1.0,
        volume: 1.0,
        pitch: 1.0,
        encoding: 'mp3'
    },
    
    // 支持的语种和对应音色
    voices: {
        'zh': [
            {value: 'zh_female_shuangkuaisisi_moon_bigtts', text: '双快思思'},
            {value: 'zh_male_jingqiangdaxiaojie_moon_bigtts', text: '京腔大小姐'},
            {value: 'zh_female_wennuanahu_moon_bigtts', text: '温暖阿虎'},
            {value: 'zh_male_zhoushenyuan_moon_bigtts', text: '周深远'}
        ],
        'en': [
            {value: 'en_female_bella_moon_bigtts', text: 'Bella'},
            {value: 'en_male_adam_moon_bigtts', text: 'Adam'},
            {value: 'en_female_sara_moon_bigtts', text: 'Sara'}
        ],
        'ja': [
            {value: 'ja_female_rei_moon_bigtts', text: 'Rei'},
            {value: 'ja_male_kenji_moon_bigtts', text: 'Kenji'}
        ],
        'ko': [
            {value: 'ko_female_soyeon_moon_bigtts', text: 'Soyeon'},
            {value: 'ko_male_minho_moon_bigtts', text: 'Minho'}
        ]
    },
    
    // 支持的情感类型
    emotions: [
        {value: 'neutral', text: '中性'},
        {value: 'happy', text: '开心'},
        {value: 'sad', text: '悲伤'},
        {value: 'angry', text: '愤怒'},
        {value: 'fearful', text: '恐惧'},
        {value: 'disgusted', text: '厌恶'},
        {value: 'surprised', text: '惊讶'}
    ]
};