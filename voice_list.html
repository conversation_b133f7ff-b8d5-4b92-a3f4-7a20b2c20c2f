<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>火山引擎TTS音色列表</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .language-section {
            margin-bottom: 40px;
        }
        .language-title {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 20px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .voice-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .voice-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
        }
        .voice-card:hover {
            background: #e9ecef;
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .voice-id {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #6c757d;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 4px;
            margin-bottom: 8px;
            display: inline-block;
        }
        .voice-name {
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }
        .category-header {
            font-size: 18px;
            font-weight: bold;
            color: #495057;
            margin: 25px 0 15px 0;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .stats {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .stats-item {
            display: inline-block;
            margin: 0 20px;
            font-weight: bold;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 10px;
            margin-left: 8px;
        }
        .copy-btn:hover {
            background: #0056b3;
        }
        .search-box {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 20px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 火山引擎TTS音色列表</h1>
        
        <div class="stats">
            <div class="stats-item">总音色数量: <span id="totalVoices">0</span></div>
            <div class="stats-item">中文音色: <span id="zhVoices">0</span></div>
            <div class="stats-item">英文音色: <span id="enVoices">0</span></div>
            <div class="stats-item">日文音色: <span id="jaVoices">0</span></div>
            <div class="stats-item">韩文音色: <span id="koVoices">0</span></div>
        </div>

        <input type="text" class="search-box" id="searchBox" placeholder="搜索音色名称或ID...">

        <div id="voiceList"></div>
    </div>

    <script src="config.js"></script>
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                alert('已复制到剪贴板: ' + text);
            });
        }

        function renderVoiceList(searchTerm = '') {
            const voiceListContainer = document.getElementById('voiceList');
            voiceListContainer.innerHTML = '';
            
            const languageNames = {
                'zh': '中文音色',
                'en': '英文音色', 
                'ja': '日文音色',
                'ko': '韩文音色'
            };

            let totalCount = 0;
            const counts = { zh: 0, en: 0, ja: 0, ko: 0 };

            Object.keys(TTS_CONFIG.voices).forEach(lang => {
                const voices = TTS_CONFIG.voices[lang];
                const filteredVoices = voices.filter(voice => 
                    voice.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    voice.text.toLowerCase().includes(searchTerm.toLowerCase())
                );

                if (filteredVoices.length === 0) return;

                const section = document.createElement('div');
                section.className = 'language-section';
                
                const title = document.createElement('div');
                title.className = 'language-title';
                title.textContent = `${languageNames[lang]} (${filteredVoices.length}个)`;
                section.appendChild(title);

                // 按类别分组
                const categories = {};
                filteredVoices.forEach(voice => {
                    let category = '其他音色';
                    if (voice.text.includes('通用') || voice.text.includes('灿灿') || voice.text.includes('活力') || voice.text.includes('温柔')) {
                        category = '通用场景音色';
                    } else if (voice.text.includes('情感')) {
                        category = '情感丰富音色';
                    } else if (voice.text.includes('专业') || voice.text.includes('商务') || voice.text.includes('教育')) {
                        category = '专业场景音色';
                    } else if (voice.text.includes('有声') || voice.text.includes('故事') || voice.text.includes('童话')) {
                        category = '有声读物音色';
                    } else if (voice.text.includes('新闻') || voice.text.includes('财经')) {
                        category = '新闻播报音色';
                    } else if (voice.text.includes('客服') || voice.text.includes('智能')) {
                        category = '客服音色';
                    } else if (voice.text.includes('话') && (voice.text.includes('东北') || voice.text.includes('粤语') || voice.text.includes('四川') || voice.text.includes('上海'))) {
                        category = '方言音色';
                    } else if (voice.text.includes('萝莉') || voice.text.includes('御姐') || voice.text.includes('大叔') || voice.text.includes('机器人')) {
                        category = '特色音色';
                    } else if (voice.value.includes('mars_bigtts')) {
                        category = '大模型音色';
                    }
                    
                    if (!categories[category]) {
                        categories[category] = [];
                    }
                    categories[category].push(voice);
                });

                Object.keys(categories).forEach(category => {
                    const categoryHeader = document.createElement('div');
                    categoryHeader.className = 'category-header';
                    categoryHeader.textContent = category;
                    section.appendChild(categoryHeader);

                    const grid = document.createElement('div');
                    grid.className = 'voice-grid';
                    
                    categories[category].forEach(voice => {
                        const card = document.createElement('div');
                        card.className = 'voice-card';
                        
                        card.innerHTML = `
                            <div class="voice-id">
                                ${voice.value}
                                <button class="copy-btn" onclick="copyToClipboard('${voice.value}')">复制</button>
                            </div>
                            <div class="voice-name">${voice.text}</div>
                        `;
                        
                        grid.appendChild(card);
                        totalCount++;
                        counts[lang]++;
                    });
                    
                    section.appendChild(grid);
                });

                voiceListContainer.appendChild(section);
            });

            // 更新统计信息
            document.getElementById('totalVoices').textContent = totalCount;
            document.getElementById('zhVoices').textContent = counts.zh;
            document.getElementById('enVoices').textContent = counts.en;
            document.getElementById('jaVoices').textContent = counts.ja;
            document.getElementById('koVoices').textContent = counts.ko;
        }

        // 搜索功能
        document.getElementById('searchBox').addEventListener('input', (e) => {
            renderVoiceList(e.target.value);
        });

        // 初始化页面
        document.addEventListener('DOMContentLoaded', () => {
            renderVoiceList();
        });
    </script>
</body>
</html>
